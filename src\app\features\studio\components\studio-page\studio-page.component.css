@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap');

:host {
    --header-height: 3rem;
    --font-medium: 500;

    --first-color: #5361ff;
    --white-color: #fafaff;
    --dark-color: #2a3b47;
    --text-color: #697477;

    --body-font: 'Montserrat', sans-serif;
    --big-font-size: 6.25rem;
    --h2-font-size: 1.25rem;
    --normal-font-size: .938rem;
    --small-font-size: .813rem;

    --mb1: .5rem;
    --mb2: 1rem;
    --mb3: 1.5rem;
    --mb4: 2rem;

    --z-fixed: 100;
}

@media screen and (min-width: 768px) {
    :host {
        --big-font-size: 7rem;
        --h2-font-size: 2rem;
        --normal-font-size: 1rem;
        --small-font-size: .875rem;
    }
}

@media screen and (max-width: 768px) {
    :host {
        --big-font-size: 2.5rem;
        --h2-font-size: 2rem;
        --normal-font-size: 1rem;
        --small-font-size: .875rem;
    }
}

*, *::before, *::after { box-sizing: border-box; }

:host {
    display: block;
    margin: var(--header-height) 0 0 0;
    font-family: var(--body-font);
    font-size: var(--normal-font-size);
    font-weight: var(--font-medium);
    color: var(--text-color);
    line-height: 1.6;
}

h1, h2, p { margin: 0; }
ul { margin: 0; padding: 0; list-style: none; }
a { text-decoration: none; color:black; }
img { max-width: 100%; height: auto; display: block; }

.section { padding: 3rem 0; }

.section-title {
    position: relative;
    font-size: var(--h2-font-size);
    color: var(--dark-color);
    margin: var(--mb4) 0;
    text-align: center;
    font-weight: 700;
}

.section-title::after {
    position: absolute;
    content: '';
    width: 32px;
    height: 0.18rem;
    left: 0;
    right: 0;
    margin: auto;
    top: 3rem;
    background-color: var(--first-color);
}

.bd-grid {
    max-width: 1024px;
    display: grid;
    grid-template-columns: 100%;
    grid-column-gap: 2rem;
    width: calc(100% - 2rem);
    margin-left: var(--mb2);
    margin-right: var(--mb2);
}

/*HEADER*/

.l-header {
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: var(--z-fixed);
    background-color: var(--first-color);
}

.nav {
    height: var(--header-height);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-item { margin-bottom: var(--mb4); }
.nav-link { position: relative; color: var(--dark-color); }
.nav-link:hover { color: var(--first-color); }
.nav-logo { color: var(--white-color); }
.nav-toggle { color: var(--white-color); font-size: 1.5rem; cursor: pointer; }

.active::after {
    position: absolute;
    content: '';
    width: 100%;
    height: 0.18rem;
    left: 0;
    top: 2rem;
    background-color: var(--first-color);
}

@media screen and (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: var(--header-height);
        right: -100%;
        width: 80%;
        height: 100%;
        padding: 2rem;
        background-color: rgba(255, 255, 255, .3);
        transition: .5s;
        backdrop-filter: blur(10px);
    }

    .show { right: 0; }
}

/*HOME*/

.home { position: relative; background-color: var(--first-color); overflow: hidden; }
.home-container { height: calc(100vh - var(--header-height)); row-gap: 5rem; }
.home-title {
    align-self: flex-end;
    font-size: var(--big-font-size);
    color: var(--white-color);
    line-height: 0.8;
    font-weight: 700;
}
.home-title span { text-shadow: 0 20px 25px rgba(0, 0, 0, .5); }

.home-scroll { align-self: flex-end; padding-bottom: var(--mb4); }
.home-scroll-link { writing-mode: vertical-lr; transform: rotate(-180deg); color: var(--white-color); }

.home-img {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 295px;
}

/*ABOUT*/

.about-container { justify-items: center; row-gap: 2rem; text-align: center; }

.about-img {
    display: flex;
    justify-content: center;
    padding-top: 1rem;
    width: 120px;
    height: 120px;
    background-color: var(--first-color);
    border-radius: 50%;
    overflow: hidden;
}

.about-img img { width: 100px; }

.about-subtitle { font-size: var(--h2-font-size); color: var(--first-color); margin-bottom: var(--mb1); font-weight: 700; }
.about-text { margin-bottom: var(--mb4); }
.about-profession { display: block; margin-bottom: var(--mb4); font-weight: 600; }
.about-social-icon { font-size: 1.4rem; margin: 0 var(--mb1); }
.about-social-icon:hover { color: var(--first-color); }

/*SKILLS*/

.skills-container { row-gap: 2rem; }
.skills-subtitle { color: var(--first-color); margin-bottom: var(--mb3); font-weight: 700; }

.skills-name {
    display: inline-block;
    font-size: var(--small-font-size);
    margin-right: var(--mb2);
    margin-bottom: var(--mb3);
    border-radius: .25rem;
    transition: .5s;
}

.skills-name:hover { background-color: var(--first-color); color: var(--white-color); }

.skills-img img { border-radius: .5rem; }

/*PORTFOLIO*/

.portfolio { background-color: var(--white-color); }
.portfolio-container { justify-items: center; row-gap: 2rem; }
.portfolio-img { position: relative; overflow: hidden; }
.portfolio-img img { border-radius: .5rem; }
.portfolio-img h1 { font-weight: 700; margin: 0.5rem 0; }
.portfolio-img p { font-weight: 500; }

.portfolio-link {
    position: absolute;
    bottom: -100%;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, .3);
    border-radius: .5rem;
    cursor: pointer;
    backdrop-filter: blur(10px);
    transition: .3s;
}

.portfolio-img:hover .portfolio-link { bottom: 0; }
.portfolio-link-color { color: var(--dark-color); }

/*CONTACT*/

.contact-container { row-gap: 2rem; }
.contact-subtitle { font-size: var(--normal-font-size); color: var(--first-color); font-weight: 700; }
.contact-text { display: inline-block; margin-bottom: var(--mb2); }
.contact-inputs { display: grid; grid-template-columns: repeat(2, 1fr); column-gap: 1rem; }

.contact-input {
    width: 100%;
    padding: 0.8rem;
    outline: none;
    border: 1.5px solid var(--dark-color);
    font-size: var(--normal-font-size);
    margin-bottom: var(--mb4);
    border-radius: .5rem;
}

.contact-button {
    display: block;
    background-color: var(--first-color);
    color: var(--white-color);
    padding: 0.75rem 2.5rem;
    margin-left: auto;
    border-radius: .5rem;
    border: none;
    outline: none;
    font-size: var(--normal-font-size);
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(83, 97, 255, 0.3);
}

.contact-button:hover {
    background-color: #4c5bff;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(83, 97, 255, 0.4);
}

/* Form validation styles */
.input-group {
    position: relative;
    margin-bottom: 1rem;
}

.contact-input.error {
    border-color: #ef4444;
    box-shadow: 0 0 0 1px #ef4444;
}

.error-message {
    color: #ef4444;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: block;
}

.alert {
    margin-bottom: 1rem;
    padding: 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
}

.alert-success {
    background-color: #dcfce7;
    border-color: #86efac;
    color: #166534;
}

.alert-error {
    background-color: #fef2f2;
    border-color: #fca5a5;
    color: #dc2626;
}

.contact-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.contact-button.loading {
    position: relative;
}

.spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s ease-in-out infinite;
    margin-right: 0.5rem;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/*FOOTER*/

.footer { background-color: var(--dark-color); }
.footer-container { row-gap: 2rem; }
.footer-title { font-size: var(--normal-font-size); color: var(--white-color); margin-bottom: var(--mb2); font-weight: 700; }
.footer-link { padding: 0.25rem 0; }
.footer-link:hover { color: var(--first-color); }

.footer-social { font-size: 1.4rem; margin-right: var(--mb1); }
.footer-social:hover { color: var(--first-color); }

/*MEDIA QUERIES*/

@media screen and (min-width: 768px) {
    :host { margin: 0; }
    .section { padding-top: 4rem; }
    .section-title { margin-bottom: 3rem; }
    .section-title::after { width: 64px; top: 3rem; }

    .nav { height: calc(var(--header-height) + 1rem); }
    .nav-list { display: flex; }
    .nav-item { margin-left: var(--mb4); margin-bottom: 0; }
    .nav-toggle { display: none; }
    .nav-link { color: var(--white-color); }
    .nav-link:hover { color: var(--white-color); }
    .active::after { background-color: var(--white-color); }

    .home-container { height: 100vh; grid-template-rows: 1.7fr 1fr; row-gap: 0; }
    .home-img { width: 524px; right: 10%; }

    .about-container { grid-template-columns: repeat(2, 1fr); align-items: center; text-align: initial; padding: 4rem 0; }
    .about-img { width: 200px; height: 200px; }
    .about-img img { width: 165px; }

    .skills-container { grid-template-columns: repeat(2, 1fr); align-items: center; }

    .portfolio-container { grid-template-columns: repeat(3, 1fr); grid-template-rows: repeat(2, 1fr); column-gap: 2rem; }

    .contact-container { grid-template-columns: repeat(2, 1fr); justify-items: center; }
    .contact-form { width: 380px; }

    .footer-container { grid-template-columns: repeat(3, 1fr); justify-items: center; }
}

@media screen and (min-width: 1024px) {
    .bd-grid { margin-left: auto; margin-right: auto; }
}

/*robohead*/

@layer properties {
@property --elh {
/* eye left height   */
  syntax: '<number>';
  inherits: true;
  initial-value: 1;
}

@property --erx {
/* eye right x pos   */
  syntax: '<percentage>';
  inherits: true;
  initial-value: 0%;
}
@property --fx {
/* face x pos   */
  syntax: '<percentage>';
  inherits: true;
  initial-value: 0%;
}
@property --ealw {
/* ear left w    */
  syntax: '<number>';
  inherits: true;
  initial-value: 1;
}@property --earw {
/* ear right w    */
  syntax: '<number>';
  inherits: true;
  initial-value: 1;
}

@property --erh {
/* eye right height   */
  syntax: '<number>';
  inherits: true;
  initial-value: 1;
}
@property --mh {
/* mouth height   */
  syntax: '<number>';
  inherits: true;
  initial-value: 1;
}
@property --mw {
/* mouth width   */
  syntax: '<number>';
  inherits: true;
  initial-value: 1;
}
}

:root {
  --surface: #111;
    --c: white;
  --c2: #9ae3dc;
  --c3: magenta;
}

.ai-bot {
  scale: 4.2;
  width: 34px;
  aspect-ratio: 1;
  position: relative;
  display: grid;
  place-items: center;
  animation: blink 2.4s ease infinite, move-head 4.2s linear(0 0%, 0 2.27%, 0.02 4.53%, 0.04 6.8%, 0.06 9.07%, 0.1 11.33%, 0.14 13.6%, 0.25 18.15%, 0.39 22.7%, 0.56 27.25%, 0.77 31.8%, 1 36.35%, 0.89 40.9%, 0.85 43.18%, 0.81 45.45%, 0.79 47.72%, 0.77 50%, 0.75 52.27%, 0.75 54.55%, 0.75 56.82%, 0.77 59.1%, 0.79 61.38%, 0.81 63.65%, 0.85 65.93%, 0.89 68.2%, 1 72.7%, 0.97 74.98%, 0.95 77.25%, 0.94 79.53%, 0.94 81.8%, 0.94 84.08%, 0.95 86.35%, 0.97 88.63%, 1 90.9%, 0.99 93.18%, 0.98 95.45%, 0.99 97.73%, 1 100%)  infinite, mouth 1.2s ease-in infinite;

}

.ai-bot .head {
  background: linear-gradient(white 80%, #cccccc, white);
  border-radius: .375rem;
  position: absolute;
  width: 28px;
  height: 20px;
  left: 200px;
  top: -60px;
}

.ai-bot .head:before,
.ai-bot .head:after {
  content: '';
  position: absolute;
  left: -4px;
  top: 6px;
  width: 2px;
  height: 8px;
  background: white;
  border-radius: 2px 0 0 2px;
  scale: var(--ealw, 1) 1;
}

.ai-bot .head:after {
  right: -4px;
  left: unset;
  border-radius: 0 2px 2px 0;
  scale: var(--earw, 1) 1;

}

.ai-bot .head .face {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: absolute;
  inset: 0 3px;
  background: #111;
  translate: var(--fx) 0;
  border-radius: 4px;
  padding: 4px 4px 2px 4px;
  gap: 3px;
}

.ai-bot .head .face:before {
  content: '';
  background: white;
  position: absolute;
  height: 1px;
  width: 10px;
  top: -2px;
  border-radius: 2px 2px 0 0;
  mask: radial-gradient(circle at 50% 100%, transparent 45%, black 45%);
}

.ai-bot .head .face .eyes {
  display: flex;
  height: 8px;
  gap: 6px;
}

.ai-bot .head .face .eyes:before,
.ai-bot .head .face .eyes:after {
  content: '';
  width: 5px;
  height: 8px;
  scale: 1 var(--elh);
  filter: drop-shadow(0 0 2px #9ae3dc);
  background: repeating-linear-gradient(to bottom, white, white .25px, transparent .25px, transparent .6px), linear-gradient(to bottom, magenta, transparent 60%), #9ae3dc;
  border-radius: 1px;
  translate: var(--erx) 0;
}

.ai-bot .head .face .eyes:after {
  scale: 1 var(--erh);
  translate: var(--erx) 0;
}

.ai-bot .head .face .mouth {
  width: 10px;
  height: 2px;
  background: #9ae3dc;
  border-radius: 0 0 1px 1px;
  filter: drop-shadow(0 0 2px #9ae3dc);
  scale: var(--mw, 1) var(--mh, 1);
}

@media (max-width: 768px) {
  .ai-bot .head {
    left: 50px;
    top: -50px;
  }
}

@layer animations {

@keyframes blink {
  from,10%, to {
    --elh: 1;
    --erh: 1;
  }

  2% {
    --elh: .2;
  }

  8% {
    --erh: .1;
  }
}

@keyframes mouth {
  from, 30%,70%, to {
  --mh: 1;
    --mw: 1;
  }

  50% {
    --mh: .2;
    --mw: .5;
  }
}

@keyframes move-head {
  from, to {
    --fx: 0%;
    --erx: 0%;
    --ealw: 1;
    --earw: 1;
  }

  25% {
    --fx: -10%;
    --erx: -20%;
    --ealw: .8;
    --earw: 1.2;
  }

  75% {
    --fx: 10%;
    --erx: 20%;
    --ealw: 1.2;
    --earw: .8;
  }
}
}

/* Button styles */
.button-main {
    display: inline-block;
    background-color: rgba(255, 255, 255, 0.9);
    color: var(--first-color);
    padding: 0.75rem 2.5rem;
    border-radius: .5rem;
    border: none;
    outline: none;
    font-size: var(--normal-font-size);
    cursor: pointer;
    margin-top: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    text-decoration: none;
}

.button-main:hover {
    background-color: rgba(255, 255, 255, 1);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    color: var(--first-color);
}

/* Three.js background styles */
#sphe-app {
    margin: 0;
    width: 100%;
    height: 100vh;
    background: radial-gradient(circle, rgba(255,255,255,1) 0%, rgba(0,0,0,0.5) 200%);
    position: relative;
    font-family: "Montserrat", serif;
    overflow: hidden;
}

#sphe-app .sphe-hero {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

#sphe-app .sphe-hero-text-background {
    position: relative;
    padding: 2rem 3rem;
    border-radius: 20px; 
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

#sphe-app .sphe-title-1,
#sphe-app .sphe-title-2 {
    margin: 0;
    padding: 0;
    color: #5361ff;
    text-transform: uppercase;
    line-height: 100%;
    user-select: none;
}

#sphe-app .sphe-title-1 {
    position: relative;
    z-index: 2;
    font-size: 100px;
    font-weight: 700;
}

#sphe-app .sphe-title-2 {
    z-index: 2;
    font-size: 80px;
    font-weight: 500;
}

@media screen and (max-width: 768px) {
    #sphe-app .sphe-title-1 {
        position: relative;
        z-index: 2;
        font-size: 50px;
        font-weight: 700;
    }
    #sphe-app .sphe-title-2 {
        z-index: 2;
        font-size: 50px;
        font-weight: 500;
    }

    #sphe-app .sphe-hero-text-background {
        padding: 1.5rem 2rem;
        margin: 0 1rem;
    }
}

#sphe-app #sphe-webgl-canvas {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: hidden;
    z-index: 1;
}
