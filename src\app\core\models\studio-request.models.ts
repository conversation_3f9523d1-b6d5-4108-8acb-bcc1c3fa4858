export interface StudioRequest {
  id: number;
  name: string;
  phone: string;
  message: string;
  created_at: string;
  status: 'new' | 'in_progress' | 'done';
}

export interface CreateStudioRequest {
  name: string;
  phone: string;
  message: string;
  status?: 'new' | 'in_progress' | 'done';
}

export interface StudioRequestsResponse {
  results?: StudioRequest[];
  count?: number;
  next?: string;
  previous?: string;
}

export const STUDIO_REQUEST_STATUS_LABELS = {
  new: 'Новая',
  in_progress: 'В работе',
  done: 'Завершена'
} as const;

export const STUDIO_REQUEST_STATUS_COLORS = {
  new: 'bg-blue-100 text-blue-800',
  in_progress: 'bg-yellow-100 text-yellow-800',
  done: 'bg-green-100 text-green-800'
} as const;
